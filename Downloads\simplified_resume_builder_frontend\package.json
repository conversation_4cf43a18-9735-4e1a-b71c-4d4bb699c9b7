{"name": "ai-resume-builder-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^5.2.5", "@google/generative-ai": "^0.14.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-slot": "^1.1.0", "@reduxjs/toolkit": "^2.2.5", "@smastrom/react-rating": "^1.5.0", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.3.6", "lucide-react": "^0.397.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-draft-wysiwyg": "^1.15.0", "react-icons": "^5.2.1", "react-redux": "^9.1.2", "react-router-dom": "^6.24.0", "react-simple-wysiwyg": "^3.0.3", "react-web-share": "^2.0.2", "sonner": "^1.5.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0"}, "devDependencies": {"@types/node": "^20.14.9", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "vite": "^5.3.1"}}